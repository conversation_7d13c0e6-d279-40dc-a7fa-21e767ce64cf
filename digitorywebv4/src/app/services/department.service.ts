import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface Department {
  id: string;
  name: string;
  code?: string;
  description?: string;
  isActive?: boolean;
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
}

export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
  virtualWorkAreas?: any[];
}

// ===== NEW GROUP-BASED INTERFACES =====
export interface DepartmentGroup {
  id: string;
  name: string;
  description?: string;
  departmentIds: string[];
  departmentNames: string[];
  color?: string;
  isActive?: boolean;
}

export interface GroupCategoryMapping {
  groupId: string;
  groupName: string;
  categories: string[];
}

export interface GroupWorkareaMapping {
  groupId: string;
  groupName: string;
  workAreas: string[];
  virtualWorkAreas?: any[];
}

export interface GroupMappingConfig {
  departmentGroups: DepartmentGroup[];
  groupCategoryMappings: GroupCategoryMapping[];
  groupWorkareaMappings: GroupWorkareaMapping[];
  lastUpdated?: string;
}

// ===== LEGACY INTERFACES (for backward compatibility) =====
export interface MappingConfig {
  departmentCategoryMappings: DepartmentCategoryMapping[];
  categoryWorkareaMappings: CategoryWorkareaMapping[];
  lastUpdated?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly engineUrl = environment.engineUrl;

  constructor(private readonly http: HttpClient) {}

  // ===== DEPARTMENT METHODS =====
  /**
   * Get all departments for a tenant (via secure backend endpoint)
   */
  getDepartments(tenantId: string): Observable<Department[]> {
    return this.http.get<any>(`${this.engineUrl}api/smart-dashboard/departments/${tenantId}`)
      .pipe(
        map(response => {
          if (response.status === 'success' && response.data) {
            const departments = response.data.map((dept: any) => ({
              id: String(dept.id),
              name: dept.name,
              code: dept.code,
              description: dept.description,
              isActive: dept.isActive !== false
            }));
            return departments;
          }
          throw new Error(response.message || 'Failed to fetch departments');
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching departments:', error);
          return throwError(() => new Error('Failed to fetch departments'));
        })
      );
  }

  // ===== DEPARTMENT-CATEGORY MAPPING METHODS =====
  /**
   * Get department-category mappings for a tenant from database
   */
  getDepartmentCategoryMappings(tenantId: string): Observable<DepartmentCategoryMapping[]> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.departmentCategoryMappings || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching department-category mappings:', error);
          return throwError(() => new Error('Failed to fetch department-category mappings'));
        })
      );
  }

  /**
   * Save department-category mappings to database
   */
  saveDepartmentCategoryMappings(tenantId: string, mappings: DepartmentCategoryMapping[]): Observable<boolean> {
    console.log('DepartmentService: saveDepartmentCategoryMappings called with:', {
      tenantId,
      mappings,
      mappingsLength: mappings.length
    });

    const requestBody = {
      tenantId: tenantId,
      departmentCategoryMappings: mappings,
      categoryWorkareaMappings: [] // Will be updated separately
    };

    console.log('DepartmentService: Request body being sent:', requestBody);

    return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error saving department-category mappings:', error);
          return throwError(() => new Error('Failed to save department-category mappings'));
        })
      );
  }

  /**
   * Get categories mapped to a specific department
   */
  getCategoriesForDepartment(tenantId: string, departmentId: string): Observable<string[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.departmentId === departmentId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Get department for a specific category
   */
  getDepartmentForCategory(tenantId: string, category: string): Observable<string | null> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categories.includes(category));
        return mapping ? mapping.departmentId : null;
      })
    );
  }

  // ===== COMPLETE MAPPING CONFIGURATION METHODS =====
  /**
   * Get complete mapping configuration (department-category + category-workarea)
   */
  getMappingConfig(tenantId: string): Observable<MappingConfig> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return {
              departmentCategoryMappings: response.data.departmentCategoryMappings || [],
              categoryWorkareaMappings: response.data.categoryWorkareaMappings || [],
              lastUpdated: response.data.lastUpdated
            };
          }
          return {
            departmentCategoryMappings: [],
            categoryWorkareaMappings: [],
            lastUpdated: undefined
          };
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching mapping config:', error);
          return throwError(() => new Error('Failed to fetch mapping configuration'));
        })
      );
  }

  /**
   * Save complete mapping configuration (department-category + category-workarea)
   */
  saveMappingConfig(tenantId: string, config: MappingConfig): Observable<boolean> {
    const requestBody = {
      tenantId: tenantId,
      departmentCategoryMappings: config.departmentCategoryMappings,
      categoryWorkareaMappings: config.categoryWorkareaMappings
    };

    console.log('DepartmentService: Sending request to API:', requestBody);

    return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
      .pipe(
        map(response => {
          console.log('DepartmentService: API response:', response);
          return response.success || false;
        }),
        catchError(error => {
          console.error('DepartmentService: Error saving mapping config:', error);
          return throwError(() => new Error('Failed to save mapping configuration'));
        })
      );
  }

  /**
   * Clear mapping configuration for a tenant
   */
  clearMappingConfig(tenantId: string): Observable<boolean> {
    return this.http.delete<any>(`${this.engineUrl}master_data/clear-mapping-config/${tenantId}`)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error clearing mapping config:', error);
          return throwError(() => new Error('Failed to clear mapping configuration'));
        })
      );
  }

  // ===== CATEGORY-WORKAREA MAPPING METHODS =====
  /**
   * Get category-workarea mappings for a tenant
   */
  getCategoryWorkareaMappings(tenantId: string): Observable<CategoryWorkareaMapping[]> {
    return this.getMappingConfig(tenantId).pipe(
      map(config => config.categoryWorkareaMappings)
    );
  }

  /**
   * Save category-workarea mappings (preserves department-category mappings)
   */
  saveCategoryWorkareaMappings(tenantId: string, mappings: CategoryWorkareaMapping[]): Observable<boolean> {
    console.log('DepartmentService: Saving category-workarea mappings for tenant:', tenantId);
    console.log('DepartmentService: Mappings to save:', mappings);

    return this.getMappingConfig(tenantId).pipe(
      switchMap((currentConfig: MappingConfig) => {
        console.log('DepartmentService: Current config:', currentConfig);

        const updatedConfig: MappingConfig = {
          departmentCategoryMappings: currentConfig.departmentCategoryMappings,
          categoryWorkareaMappings: mappings
        };

        console.log('DepartmentService: Updated config to save:', updatedConfig);
        return this.saveMappingConfig(tenantId, updatedConfig);
      })
    );
  }

  /**
   * Get workareas mapped to a specific category
   */
  getWorkareasForCategory(tenantId: string, categoryName: string): Observable<string[]> {
    return this.getCategoryWorkareaMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categoryName === categoryName);
        return mapping ? mapping.workAreas : [];
      })
    );
  }

  /**
   * Get category for a specific workarea
   */
  getCategoryForWorkarea(tenantId: string, workarea: string): Observable<string | null> {
    return this.getCategoryWorkareaMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.workAreas.includes(workarea));
        return mapping ? mapping.categoryName : null;
      })
    );
  }

  // ===== GROUP MANAGEMENT METHODS =====

  /**
   * Get department groups for a tenant
   */
  getDepartmentGroups(tenantId: string): Observable<DepartmentGroup[]> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-group-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.departmentGroups || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching department groups:', error);
          return throwError(() => new Error('Failed to fetch department groups'));
        })
      );
  }

  /**
   * Save department groups
   */
  saveDepartmentGroups(tenantId: string, groups: DepartmentGroup[]): Observable<boolean> {
    const requestBody = {
      tenantId: tenantId,
      departmentGroups: groups,
      groupCategoryMappings: [],
      groupWorkareaMappings: []
    };

    return this.http.post<any>(`${this.engineUrl}master_data/save-group-mapping-config`, requestBody)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error saving department groups:', error);
          return throwError(() => new Error('Failed to save department groups'));
        })
      );
  }

  /**
   * Get complete group mapping configuration
   */
  getGroupMappingConfig(tenantId: string): Observable<GroupMappingConfig> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-group-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return {
              departmentGroups: response.data.departmentGroups || [],
              groupCategoryMappings: response.data.groupCategoryMappings || [],
              groupWorkareaMappings: response.data.groupWorkareaMappings || [],
              lastUpdated: response.data.lastUpdated
            };
          }
          return {
            departmentGroups: [],
            groupCategoryMappings: [],
            groupWorkareaMappings: [],
            lastUpdated: undefined
          };
        }),
        catchError(error => {
          console.error('DepartmentService: Error fetching group mapping config:', error);
          return throwError(() => new Error('Failed to fetch group mapping configuration'));
        })
      );
  }

  /**
   * Save complete group mapping configuration
   */
  saveGroupMappingConfig(tenantId: string, config: GroupMappingConfig): Observable<boolean> {
    const requestBody = {
      tenantId: tenantId,
      departmentGroups: config.departmentGroups,
      groupCategoryMappings: config.groupCategoryMappings,
      groupWorkareaMappings: config.groupWorkareaMappings
    };

    return this.http.post<any>(`${this.engineUrl}master_data/save-group-mapping-config`, requestBody)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentService: Error saving group mapping config:', error);
          return throwError(() => new Error('Failed to save group mapping configuration'));
        })
      );
  }

  /**
   * Save group-category mappings
   */
  saveGroupCategoryMappings(tenantId: string, mappings: GroupCategoryMapping[]): Observable<boolean> {
    return this.getGroupMappingConfig(tenantId).pipe(
      switchMap((currentConfig: GroupMappingConfig) => {
        const updatedConfig: GroupMappingConfig = {
          departmentGroups: currentConfig.departmentGroups,
          groupCategoryMappings: mappings,
          groupWorkareaMappings: currentConfig.groupWorkareaMappings
        };
        return this.saveGroupMappingConfig(tenantId, updatedConfig);
      })
    );
  }

  /**
   * Save group-workarea mappings
   */
  saveGroupWorkareaMappings(tenantId: string, mappings: GroupWorkareaMapping[]): Observable<boolean> {
    return this.getGroupMappingConfig(tenantId).pipe(
      switchMap((currentConfig: GroupMappingConfig) => {
        const updatedConfig: GroupMappingConfig = {
          departmentGroups: currentConfig.departmentGroups,
          groupCategoryMappings: currentConfig.groupCategoryMappings,
          groupWorkareaMappings: mappings
        };
        return this.saveGroupMappingConfig(tenantId, updatedConfig);
      })
    );
  }

  // ===== UTILITY METHODS =====

  /**
   * Validate department-category mapping rules
   * - Single department can have multiple categories
   * - Single category can only be mapped to one department
   */
  validateMappings(mappings: DepartmentCategoryMapping[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const categoryToDepartment = new Map<string, string>();

    for (const mapping of mappings) {
      for (const category of mapping.categories) {
        if (categoryToDepartment.has(category)) {
          const existingDept = categoryToDepartment.get(category);
          if (existingDept !== mapping.departmentId) {
            errors.push(`Category "${category}" is mapped to multiple departments: "${existingDept}" and "${mapping.departmentName}"`);
          }
        } else {
          categoryToDepartment.set(category, mapping.departmentId);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate group-category mapping rules
   * - Single group can have multiple categories
   * - Single category can only be mapped to one group
   */
  validateGroupMappings(mappings: GroupCategoryMapping[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const categoryToGroup = new Map<string, string>();

    for (const mapping of mappings) {
      for (const category of mapping.categories) {
        if (categoryToGroup.has(category)) {
          const existingGroup = categoryToGroup.get(category);
          if (existingGroup !== mapping.groupId) {
            errors.push(`Category "${category}" is mapped to multiple groups: "${existingGroup}" and "${mapping.groupName}"`);
          }
        } else {
          categoryToGroup.set(category, mapping.groupId);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
