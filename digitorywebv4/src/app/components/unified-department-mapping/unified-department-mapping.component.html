<div class="unified-department-mapping" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading departments and categories...</p>
  </div>

  <!-- Compact Single-View Interface -->
  <div *ngIf="!isLoading" class="compact-mapping-content">

    <!-- Header with Add Group Button -->
    <div class="compact-header">
      <div class="header-info">
        <h4>Create department groups and assign departments to each group. You can create multiple groups to organize your departments.</h4>
      </div>
      <button mat-raised-button color="primary" (click)="addNewGroup()" class="add-group-btn">
        <mat-icon>add</mat-icon>
        Add New Group
      </button>
      <span class="groups-count" *ngIf="groupFormArray.length > 0">
        {{groupFormArray.length}} group(s) created
      </span>
    </div>

    <!-- Compact Groups List -->
    <form [formGroup]="groupForm">
      <div formArrayName="groups" class="compact-groups-list">
        <div *ngFor="let groupControl of groupFormArray.controls; let i = index; trackBy: trackByIndex"
             [formGroupName]="i" class="compact-group-card">

          <!-- Group Header with inline controls -->
          <div class="compact-group-header">
            <div class="group-badge">{{i + 1}}</div>

            <mat-form-field appearance="outline" class="compact-name-field">
              <input matInput formControlName="name" placeholder="Group Name *">
            </mat-form-field>

            <mat-form-field appearance="outline" class="compact-color-field">
              <input matInput type="color" formControlName="color">
            </mat-form-field>

            <button mat-icon-button color="warn" (click)="removeGroup(i)"
                    [disabled]="groupFormArray.length <= 1"
                    matTooltip="Delete group">
              <mat-icon>delete</mat-icon>
            </button>
          </div>

          <!-- Departments Selection - Inline -->
          <div class="compact-departments-row">
            <mat-form-field appearance="outline" class="compact-dept-select">
              <mat-select [value]="groupControl.get('departmentIds')?.value"
                         (selectionChange)="onGroupDepartmentSelectionChange(i, $event.value)"
                         multiple
                         placeholder="Select Departments">
                <mat-option *ngFor="let department of getAvailableDepartmentsForGroup(i)" [value]="department.id">
                  {{department.name}} <span *ngIf="department.code">({{department.code}})</span>
                </mat-option>
              </mat-select>
            </mat-form-field>

            <!-- Selected Departments Chips - Inline -->
            <div class="compact-selected-chips" *ngIf="groupControl.get('departmentNames')?.value?.length > 0">
              <mat-chip-listbox>
                <mat-chip-option *ngFor="let deptName of groupControl.get('departmentNames')?.value"
                                 [style.background-color]="groupControl.get('color')?.value"
                                 [style.color]="'white'">
                  {{deptName}}
                </mat-chip-option>
              </mat-chip-listbox>
            </div>
          </div>
        </div>
      </div>
    </form>

    <!-- Empty State -->
    <div *ngIf="groupFormArray.length === 0" class="compact-empty-state">
      <mat-icon>group_work</mat-icon>
      <p>No groups created yet. Click "Add New Group" to get started.</p>
    </div>

    <!-- Save Section -->
    <div class="compact-save-section">
      <button mat-raised-button
              color="primary"
              (click)="saveGroupConfiguration()"
              [disabled]="isSaving"
              class="compact-save-button">
        <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
        <mat-icon *ngIf="!isSaving">save</mat-icon>
        {{ isSaving ? 'Saving...' : 'Save Configuration' }}
      </button>

    <!-- Dialog Actions (only show when in dialog mode) -->
    <div class="dialog-actions" *ngIf="showAsDialog">
      <button mat-raised-button (click)="closeDialog()">
        <mat-icon>close</mat-icon>
        Cancel
      </button>

      <button mat-raised-button color="primary"
              (click)="saveGroupConfiguration()"
              [disabled]="isSaving"
              class="save-button">
        <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
        <mat-icon *ngIf="!isSaving">save</mat-icon>
        {{ isSaving ? 'Saving...' : 'Save & Close' }}
      </button>
    </div>
  </div>
</div>
