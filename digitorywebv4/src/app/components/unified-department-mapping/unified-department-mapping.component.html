<div class="unified-department-mapping" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading departments and categories...</p>
  </div>

  <!-- Main Content - NEW 3-Step Workflow -->
  <div *ngIf="!isLoading" class="mapping-content">

    <!-- Step Progress Indicator -->
    <div class="step-progress">
      <div class="step-indicator">
        <div class="step" [class.active]="currentStep === 0" [class.completed]="currentStep > 0"
             [class.clickable]="groupFormArray.length > 0" (click)="goToStep(0)">
          <div class="step-number">1</div>
          <div class="step-label">Create Groups</div>
        </div>
        <div class="step-connector" [class.completed]="currentStep > 0"></div>
        <div class="step" [class.active]="currentStep === 1" [class.completed]="currentStep > 1"
             [class.clickable]="groupFormArray.length > 0"
             [class.disabled]="groupFormArray.length === 0"
             (click)="groupFormArray.length > 0 && goToStep(1)">
          <div class="step-number">2</div>
          <div class="step-label">Map Categories</div>
        </div>
        <div class="step-connector" [class.completed]="currentStep > 1"></div>
        <div class="step" [class.active]="currentStep === 2" [class.completed]="currentStep > 2"
             [class.clickable]="groupCategoryFormArray.length > 0"
             [class.disabled]="groupCategoryFormArray.length === 0"
             (click)="groupCategoryFormArray.length > 0 && goToStep(2)">
          <div class="step-number">3</div>
          <div class="step-label">Map Workareas</div>
        </div>
      </div>
    </div>

    <!-- Step 1: Create Groups and Map Departments -->
    <div *ngIf="currentStep === 0" class="step-content">
      <div class="section-header">
        <mat-icon>group_work</mat-icon>
        <h4>Step 1: Create Groups and Map Departments</h4>
        <p>Create department groups and assign departments to each group. You can create multiple groups to organize your departments.</p>
      </div>

      <div class="groups-container">
        <!-- Instructions Card -->
        <div class="instructions-card" *ngIf="groupFormArray.length === 0">
          <div class="instructions-content">
            <mat-icon class="instructions-icon">info</mat-icon>
            <div class="instructions-text">
              <h5>Getting Started</h5>
              <p>Create your first group by clicking the "Add New Group" button below. You can create multiple groups to organize your departments (e.g., "Food Department", "Beverage Department", etc.).</p>
            </div>
          </div>
        </div>

        <div class="groups-header">
          <button mat-raised-button color="primary" (click)="addNewGroup()" class="add-group-btn">
            <mat-icon>add</mat-icon>
            Add New Group
          </button>
          <span class="groups-count" *ngIf="groupFormArray.length > 0">
            {{groupFormArray.length}} group(s) created
          </span>
        </div>

        <form [formGroup]="groupForm">
          <div formArrayName="groups" class="groups-list">
            <div *ngFor="let groupControl of groupFormArray.controls; let i = index; trackBy: trackByIndex"
                 [formGroupName]="i" class="group-card">

              <div class="group-card-header">
                <div class="group-number">Group {{i + 1}}</div>
                <button mat-icon-button color="warn" (click)="removeGroup(i)"
                        [disabled]="groupFormArray.length <= 1"
                        matTooltip="Delete this group">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>

              <div class="group-basic-info">
                <mat-form-field appearance="outline" class="group-name-field">
                  <mat-label>Group Name *</mat-label>
                  <input matInput formControlName="name" placeholder="e.g., Food Department, Beverage Department">
                </mat-form-field>

                <mat-form-field appearance="outline" class="group-color-field">
                  <mat-label>Color</mat-label>
                  <input matInput type="color" formControlName="color">
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="group-description-field">
                <mat-label>Description (Optional)</mat-label>
                <textarea matInput formControlName="description"
                         placeholder="Brief description of this group" rows="2"></textarea>
              </mat-form-field>

              <div class="department-selection-section">
                <h5>Assign Departments to this Group</h5>
                <mat-form-field appearance="outline" class="department-select-field">
                  <mat-label>Select Departments</mat-label>
                  <mat-select [value]="groupControl.get('departmentIds')?.value"
                             (selectionChange)="onGroupDepartmentSelectionChange(i, $event.value)"
                             multiple>
                    <mat-option *ngFor="let department of getAvailableDepartmentsForGroup(i)" [value]="department.id">
                      {{department.name}} <span *ngIf="department.code">({{department.code}})</span>
                    </mat-option>
                  </mat-select>
                  <mat-hint>Select one or more departments for this group</mat-hint>
                </mat-form-field>
              </div>

              <!-- Selected Departments Display -->
              <div *ngIf="groupControl.get('departmentNames')?.value?.length > 0" class="selected-departments">
                <h5>Selected Departments ({{groupControl.get('departmentNames')?.value?.length}}):</h5>
                <mat-chip-listbox>
                  <mat-chip-option *ngFor="let deptName of groupControl.get('departmentNames')?.value"
                                   [style.background-color]="groupControl.get('color')?.value"
                                   [style.color]="'white'">
                    {{deptName}}
                  </mat-chip-option>
                </mat-chip-listbox>
              </div>

              <!-- Warning if no departments selected -->
              <div *ngIf="groupControl.get('departmentNames')?.value?.length === 0" class="no-departments-warning">
                <mat-icon class="warning-icon">warning</mat-icon>
                <span>Please select at least one department for this group</span>
              </div>
            </div>
          </div>
        </form>

        <div *ngIf="groupFormArray.length === 0" class="empty-state-simple">
          <mat-icon>group_work</mat-icon>
          <p>No groups created yet. Click "Add New Group" above to get started.</p>
        </div>
      </div>
    </div>

    <!-- Step 2: Map Groups with Categories -->
    <div *ngIf="currentStep === 1" class="step-content">
      <div class="section-header">
        <mat-icon>category</mat-icon>
        <h4>Step 2: Map Groups with Categories</h4>
        <p>Assign categories to each department group</p>
      </div>

      <form [formGroup]="groupCategoryForm">
        <div formArrayName="mappings" class="mappings-container">
          <div *ngFor="let mappingGroup of groupCategoryFormArray.controls; let i = index; trackBy: trackByIndex"
               [formGroupName]="i" class="mapping-row">

            <!-- Group Info -->
            <div class="group-info">
              <mat-icon class="group-icon">group_work</mat-icon>
              <div class="group-details">
                <h5>{{mappingGroup.get('groupName')?.value}}</h5>
                <span class="group-id">ID: {{mappingGroup.get('groupId')?.value}}</span>
              </div>
            </div>

            <!-- Category Selection -->
            <mat-form-field appearance="outline" class="category-select-field">
              <mat-label>Select Categories</mat-label>
              <mat-select formControlName="categories" multiple>
                <mat-option *ngFor="let category of getAvailableCategoriesForGroup(i)" [value]="category">
                  {{category}}
                </mat-option>
              </mat-select>
              <mat-hint>
                {{mappingGroup.get('categories')?.value?.length || 0}} categories selected
              </mat-hint>
            </mat-form-field>
          </div>
        </div>
      </form>

      <div *ngIf="groupCategoryFormArray.length === 0" class="no-selection-message">
        <mat-icon>arrow_back</mat-icon>
        <p>Create groups in Step 1 to configure category mappings</p>
      </div>

      <!-- No Categories Available Message -->
      <div *ngIf="categories.length === 0" class="no-data-message">
        <mat-icon>info</mat-icon>
        <p>No categories available for mapping</p>
      </div>
    </div>

    <!-- Step 3: Map Groups with Workareas -->
    <div *ngIf="currentStep === 2" class="step-content">
      <div class="section-header">
        <mat-icon>work</mat-icon>
        <h4>Step 3: Map Groups with Workareas</h4>
        <p>Assign work areas to each department group</p>
      </div>

      <div class="workarea-mapping-container">
        <!-- Smart Mapping Info -->
        <div class="smart-mapping-info">
          <div class="info-card">
            <mat-icon class="info-icon">auto_awesome</mat-icon>
            <div class="info-content">
              <h4>Smart Mapping Enabled</h4>
              <p>Select any workarea for any group. The system will automatically handle conflicts and create unique mappings when needed.</p>
            </div>
          </div>
        </div>

        <!-- Group-Workarea Mapping Form -->
        <form [formGroup]="groupWorkareaForm" class="mapping-form">
          <div formArrayName="mappings" class="mappings-container">
            <!-- Empty State Message -->
            <div *ngIf="groupWorkareaFormArray.controls.length === 0" class="empty-state-message">
              <mat-icon class="empty-icon">arrow_back</mat-icon>
              <p>Assign categories to groups in Step 2 to configure work area mappings</p>
            </div>

            <!-- Mapping Rows -->
            <div *ngFor="let mappingGroup of groupWorkareaFormArray.controls; let i = index; trackBy: trackByIndex"
              [formGroupName]="i" class="mapping-row">
              <div class="group-name">
                <mat-icon class="group-icon">group_work</mat-icon>
                <span>{{ mappingGroup.get('groupName')?.value || 'Unknown Group' }}</span>
              </div>

              <div class="workareas-dropdown">
                <mat-form-field appearance="outline" class="workareas-field">
                  <mat-label>Select Work Areas</mat-label>
                  <mat-select formControlName="workAreas" multiple>
                    <mat-option *ngFor="let workArea of getAvailableWorkAreasForGroup(i)" [value]="workArea">
                      {{ workArea }}
                    </mat-option>
                  </mat-select>
                  <mat-hint>
                    {{mappingGroup.get('workAreas')?.value?.length || 0}} work areas selected
                  </mat-hint>
                </mat-form-field>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step Navigation -->
    <div class="step-navigation">
      <div class="nav-buttons">
        <button mat-raised-button
                (click)="previousStep()"
                [disabled]="currentStep === 0"
                class="nav-button">
          <mat-icon>arrow_back</mat-icon>
          Previous
        </button>

        <button mat-raised-button
                color="primary"
                (click)="nextStep()"
                [disabled]="!canProceedToNextStep()"
                class="nav-button">
          Next
          <mat-icon>arrow_forward</mat-icon>
        </button>
      </div>

      <!-- Step validation messages -->
      <div class="step-validation" *ngIf="!canProceedToNextStep() && currentStep < maxSteps - 1">
        <mat-icon class="validation-icon">info</mat-icon>
        <span class="validation-message">{{ getStepValidationMessage() }}</span>
      </div>

      <!-- Save Button -->
      <div class="save-section">
        <button mat-raised-button
                color="accent"
                (click)="saveGroupConfiguration()"
                [disabled]="isSaving"
                class="save-button">
          <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
          <mat-icon *ngIf="!isSaving">save</mat-icon>
          {{ isSaving ? 'Saving...' : 'Save Configuration' }}
        </button>
      </div>
    </div>

    <!-- Dialog Actions (only show when in dialog mode) -->
    <div class="dialog-actions" *ngIf="showAsDialog">
      <button mat-raised-button (click)="closeDialog()">
        <mat-icon>close</mat-icon>
        Cancel
      </button>

      <button mat-raised-button color="primary"
              (click)="saveGroupConfiguration()"
              [disabled]="isSaving"
              class="save-button">
        <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
        <mat-icon *ngIf="!isSaving">save</mat-icon>
        {{ isSaving ? 'Saving...' : 'Save & Close' }}
      </button>
    </div>
  </div>
</div>
