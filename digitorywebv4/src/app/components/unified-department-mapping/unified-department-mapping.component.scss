// Compact Single-View Styles
.unified-department-mapping {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  &.dialog-mode {
    padding: 0;
    max-width: none;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    mat-spinner {
      margin-bottom: 20px;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  // Compact single-view layout
  .compact-mapping-content {
    .compact-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;
      padding: 16px 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .header-info {
        flex: 1;

        h4 {
          margin: 0;
          color: #333;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
        }
      }

      .add-group-btn {
        background: #ff6b35;
        color: white;
        font-weight: 500;
        margin-left: 16px;

        &:hover {
          background: #ff5722;
        }
      }

      .groups-count {
        color: #666;
        font-size: 12px;
        font-weight: 500;
        margin-left: 12px;
        white-space: nowrap;
      }
    }

      &.completed {
        .step-number {
          background: #4caf50;
          border-color: #4caf50;
          color: white;
        }

        .step-label {
          color: #4caf50;
        }
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;

        .step-number {
          background: #f5f5f5;
          border-color: #e0e0e0;
          color: #ccc;
        }

        .step-label {
          color: #ccc;
        }
      }

      &.clickable {
        cursor: pointer;

        &:hover:not(.disabled) {
          .step-number {
            transform: scale(1.05);
          }
        }
      }
    }

    .step-connector {
      flex: 1;
      height: 2px;
      background: #e0e0e0;
      transition: background 0.3s ease;

      &.completed {
        background: #4caf50;
      }
    }
  }
}

.step-content {
  min-height: 400px;
  margin-bottom: 30px;
}

// Instructions card
.instructions-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #e1bee7;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;

  .instructions-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .instructions-icon {
      color: #7b1fa2;
      font-size: 24px;
      width: 24px;
      height: 24px;
      margin-top: 2px;
    }

    .instructions-text {
      flex: 1;

      h5 {
        margin: 0 0 8px 0;
        color: #4a148c;
        font-weight: 600;
        font-size: 16px;
      }

      p {
        margin: 0;
        color: #6a1b9a;
        line-height: 1.5;
        font-size: 14px;
      }
    }
  }
}

// Group creation styles
.groups-container {
  .groups-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .add-group-btn {
      background: #ff6b35;
      color: white;
      font-weight: 500;

      &:hover {
        background: #ff5722;
      }
    }

    .groups-count {
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .groups-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .group-card {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 24px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      border-color: #ff6b35;
      box-shadow: 0 4px 16px rgba(255, 107, 53, 0.1);
    }

    .group-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .group-number {
        background: #ff6b35;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .group-basic-info {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .group-name-field {
        flex: 1;
      }

      .group-color-field {
        width: 120px;
      }
    }

    .group-description-field {
      width: 100%;
      margin-bottom: 20px;
    }

    .department-selection-section {
      margin-bottom: 16px;

      h5 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 14px;
        font-weight: 600;
      }

      .department-select-field {
        width: 100%;
      }
    }

    .selected-departments {
      margin-top: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      h5 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 14px;
        font-weight: 600;
      }

      mat-chip-listbox {
        mat-chip-option {
          margin: 4px;
          font-weight: 500;
          border-radius: 16px;
        }
      }
    }

    .no-departments-warning {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 6px;
      margin-top: 16px;

      .warning-icon {
        color: #856404;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      span {
        color: #856404;
        font-size: 13px;
        font-weight: 500;
      }
    }
  }

  .empty-state-simple {
    text-align: center;
    padding: 30px;
    color: #666;
    background: #f9f9f9;
    border: 2px dashed #ddd;
    border-radius: 8px;
    margin-top: 20px;

    mat-icon {
      font-size: 36px;
      width: 36px;
      height: 36px;
      margin-bottom: 12px;
      color: #ccc;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

// Step navigation styles
.step-navigation {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px 0;
  border-top: 2px solid #f0f0f0;
  margin-top: 30px;

  .nav-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .nav-button {
      min-width: 140px;
      height: 44px;
      font-weight: 500;

      &[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:not([disabled]):hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .step-validation {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;

    .validation-icon {
      color: #856404;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .validation-message {
      color: #856404;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .save-section {
    display: flex;
    justify-content: center;

    .save-button {
      background: #4caf50;
      color: white;
      min-width: 180px;
      height: 48px;
      font-weight: 600;
      font-size: 16px;

      &:hover:not([disabled]) {
        background: #45a049;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
      }

      &[disabled] {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .button-spinner {
        margin-right: 8px;
      }
    }
  }
}

    .dialog-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding: 10px;
      border-top: 1px solid #e0e0e0;
      background: #fafafa;

      .cancel-btn {
        color: #666;

        &:hover {
          background: #f0f0f0;
        }
      }

      .save-btn {
        background: #ff6b35;
        color: white;

        &:hover {
          background: #ff5722;
        }
      }
    }

.unified-department-mapping {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  // When shown in dialog, remove background and shadow
  &.dialog-mode {
    background: transparent;
    box-shadow: none;
    border-radius: 0;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;

    mat-spinner {
      margin-bottom: 16px;
    }

    p {
      color: #666;
      margin: 0;
    }
  }

  .mapping-content {
    .section-header {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 2px solid #f0f0f0;

      .header-title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        mat-icon {
          margin-right: 8px;
          color: #ff6b35;
        }

        h4 {
          margin: 0;
          color: #333;
          font-weight: 500;
        }
      }

      // Support both old and new header formats
      mat-icon {
        margin-right: 8px;
        color: #ff6b35;
      }

      h4 {
        margin: 0;
        color: #333;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .departments-section {
      margin-bottom: 24px;

      .department-filter-field {
        width: 100%;
        max-width: 600px;

        ::ng-deep {
          .mat-mdc-form-field-wrapper {
            height: 40px;
            min-height: 40px;
          }

          .mat-mdc-form-field-infix {
            padding: 8px 12px;
            min-height: 24px;
          }

          .mat-mdc-form-field-subscript-wrapper {
            display: none;
          }
        }
      }

      .select-all-custom-option {
        padding: 12px 16px;
        cursor: pointer;
        color: #ff6b35;
        font-weight: 500;
        border-bottom: 1px solid #eee;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }

    .department-mapping-section {
      margin-bottom: 24px;

      .mappings-container {
        .mapping-row {
          display: flex;
          align-items: center;
          gap: 20px;
          padding: 16px;
          margin-bottom: 12px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background: #fafafa;

          .department-info {
            display: flex;
            align-items: center;
            min-width: 200px;
            flex-shrink: 0;

            .department-icon {
              margin-right: 12px;
              color: #ff6b35;
            }

            .department-details {
              h5 {
                margin: 0 0 4px 0;
                font-weight: 500;
                color: #333;
              }

              .department-id {
                font-size: 12px;
                color: #666;
              }
            }
          }

          .category-selection {
            flex: 1;

            .categories-field {
              width: 100%;

              ::ng-deep {
                .mat-mdc-form-field-wrapper {
                  height: 40px;
                  min-height: 40px;
                }

                .mat-mdc-form-field-infix {
                  padding: 8px 12px;
                  min-height: 24px;
                }

                .mat-mdc-form-field-subscript-wrapper {
                  display: none;
                }
              }
            }
          }
        }
      }
    }

    .category-workarea-section {
      margin-bottom: 24px;

      .workarea-mapping-container {
        .selected-categories-info {
          padding: 12px 16px;
          background: #f0f8ff;
          border: 1px solid #e3f2fd;
          border-radius: 6px;
          margin-bottom: 16px;

          p {
            margin: 0;
            color: #1976d2;
            font-size: 14px;

            strong {
              color: #0d47a1;
            }
          }
        }

        .category-workarea-mapping-wrapper {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          background: #fafafa;
        }
      }
    }

    .save-actions {
      margin-bottom: 24px;

      .save-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;

        .save-info {
          display: flex;
          align-items: center;
          gap: 12px;

          mat-icon {
            color: #ff6b35;
            font-size: 24px;
            width: 24px;
            height: 24px;
          }

          .save-text {
            h4 {
              margin: 0 0 4px 0;
              color: #333;
              font-weight: 500;
              font-size: 16px;
            }

            p {
              margin: 0;
              color: #666;
              font-size: 14px;
            }
          }
        }

        .save-button {
          min-width: 200px;
          height: 40px;

          .button-spinner {
            margin-right: 8px;
          }

          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }

    .summary-section {
      margin-bottom: 24px;

      .summary-cards {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;

        .summary-card {
          flex: 1;
          min-width: 150px;
          padding: 16px;
          background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
          color: white;
          border-radius: 8px;
          text-align: center;

          .summary-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .summary-label {
            font-size: 12px;
            opacity: 0.9;
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      color: #666;

      .empty-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #ccc;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        color: #333;
      }

      p {
        margin: 0;
        max-width: 400px;
        margin: 0 auto;
      }
    }

    .no-data-message {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #666;
      background: #f9f9f9;
      border-radius: 8px;
      border: 1px dashed #ddd;

      mat-icon {
        margin-right: 8px;
        color: #999;
      }

      p {
        margin: 0;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .unified-department-mapping {
    padding: 16px;

    .mapping-content {
      .department-mapping-section {
        .mappings-container {
          .mapping-row {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;

            .department-info {
              min-width: auto;
            }
          }
        }
      }

      .summary-section {
        .summary-cards {
          flex-direction: column;

          .summary-card {
            min-width: auto;
          }
        }
      }
    }
  }
}

// Material Design overrides
::ng-deep {
  .mat-mdc-select-panel {
    max-height: 400px;
  }

  .mat-mdc-option {
    &.mat-mdc-option-multiple {
      .mat-pseudo-checkbox {
        margin-right: 8px;
      }
    }
  }

  .mat-mdc-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 4px;
    }
  }

  // ===== CATEGORY-WORKAREA MAPPING STYLES =====

  .category-workarea-section {
    margin-top: 24px;

    .workarea-mapping-container {
      .selected-categories-info {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 16px;
        border-left: 3px solid #ff6b35;

        p {
          margin: 0;
          font-size: 14px;
          color: #333;
        }
      }

      .smart-mapping-info {
        margin-bottom: 16px;

        .info-card {
          display: flex;
          align-items: flex-start;
          gap: 10px;
          padding: 12px;
          background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
          border-radius: 6px;
          border: 1px solid #c8e6c9;
          border-left: 3px solid #4caf50;

          .info-icon {
            font-size: 20px;
            color: #4caf50;
            margin-top: 1px;
            flex-shrink: 0;
          }

          .info-content {
            flex: 1;

            h4 {
              margin: 0 0 2px 0;
              font-size: 14px;
              font-weight: 600;
              color: #2e7d32;
            }

            p {
              margin: 0;
              font-size: 12px;
              color: #388e3c;
              line-height: 1.3;
            }
          }
        }
      }

      .validation-errors {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
        background: #ffebee;
        border-radius: 6px;
        border-left: 3px solid #f44336;
        margin-bottom: 16px;

        .error-icon {
          color: #f44336;
          font-size: 20px;
          flex-shrink: 0;
        }

        .error-list {
          flex: 1;

          .error-message {
            margin: 0 0 4px 0;
            font-size: 13px;
            color: #c62828;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .mapping-form {
        .mappings-container {
          .empty-state-message {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 40px 20px;
            text-align: center;
            color: #666;
            background: #f8f9fa;
            border-radius: 6px;
            border: 2px dashed #dee2e6;

            .empty-icon {
              font-size: 24px;
              color: #adb5bd;
            }

            p {
              margin: 0;
              font-size: 14px;
            }
          }

          .mapping-row {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px;
            margin-bottom: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;

            .category-name {
              display: flex;
              align-items: center;
              gap: 8px;
              min-width: 200px;
              font-weight: 500;
              color: #333;

              .category-icon {
                color: #ff6b35;
                font-size: 18px;
              }
            }

            .workareas-dropdown {
              flex: 1;

              .workareas-field {
                width: 100%;

                ::ng-deep {
                  .mat-mdc-form-field-wrapper {
                    height: 40px;
                    min-height: 40px;
                  }

                  .mat-mdc-form-field-infix {
                    padding: 8px 12px;
                    min-height: 24px;
                  }

                  .mat-mdc-form-field-subscript-wrapper {
                    display: none;
                  }
                }
              }
            }
          }
        }
      }
    }
    // NEW: Group-Category and Group-Workarea mapping styles
    .mappings-container {
      .mapping-row {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 12px;
        background: #fafafa;

        .group-info,
        .group-details {
          display: flex;
          align-items: center;
          min-width: 200px;

          .group-icon {
            margin-right: 12px;
            color: #ff6b35;
          }

          h5 {
            margin: 0 0 4px 0;
            color: #333;
            font-weight: 500;
          }

          .group-id {
            font-size: 12px;
            color: #666;
          }
        }

        .category-select-field,
        .workareas-field {
          flex: 1;

          ::ng-deep {
            .mat-mdc-form-field-wrapper {
              min-height: 48px;
            }
          }
        }

        .group-name {
          display: flex;
          align-items: center;
          min-width: 200px;

          .group-icon {
            margin-right: 12px;
            color: #ff6b35;
          }

          span {
            font-weight: 500;
            color: #333;
          }
        }

        .workareas-dropdown {
          flex: 1;
        }
      }
    }

    .no-selection-message {
      text-align: center;
      padding: 40px;
      color: #666;

      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        margin-bottom: 16px;
        color: #ccc;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }

    .no-data-message {
      text-align: center;
      padding: 30px;
      color: #666;
      background: #f9f9f9;
      border-radius: 8px;
      border: 1px dashed #ddd;

      mat-icon {
        font-size: 36px;
        width: 36px;
        height: 36px;
        margin-bottom: 12px;
        color: #ccc;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    // Smart mapping info styles
    .smart-mapping-info {
      margin-bottom: 20px;

      .info-card {
        display: flex;
        align-items: flex-start;
        padding: 16px;
        background: #e8f5e8;
        border: 1px solid #c8e6c9;
        border-radius: 8px;

        .info-icon {
          margin-right: 12px;
          color: #4caf50;
          margin-top: 2px;
        }

        .info-content {
          h4 {
            margin: 0 0 8px 0;
            color: #2e7d32;
            font-size: 16px;
            font-weight: 500;
          }

          p {
            margin: 0;
            color: #388e3c;
            font-size: 14px;
            line-height: 1.4;
          }
        }
      }
    }

    // Summary section styles
    .summary-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;

      .summary-cards {
        display: flex;
        gap: 20px;
        margin-top: 16px;

        .summary-card {
          flex: 1;
          text-align: center;
          padding: 20px;
          background: #f5f5f5;
          border-radius: 8px;
          border: 1px solid #e0e0e0;

          .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 8px;
          }

          .summary-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }

    // Empty state styles
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;

      .empty-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        margin-bottom: 20px;
        color: #ddd;
      }

      h3 {
        margin: 0 0 12px 0;
        color: #333;
        font-weight: 500;
      }

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
        max-width: 400px;
        margin: 0 auto;
      }
    }
  }
}

// Compact groups list styles
.compact-groups-list {
  .compact-group-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background: #fff;
    transition: all 0.3s ease;

    &:hover {
      border-color: #ff6b35;
      box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
    }

    .compact-group-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;

      .group-badge {
        background: #ff6b35;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        min-width: 20px;
        text-align: center;
      }

      .compact-name-field {
        flex: 1;

        .mat-mdc-form-field-wrapper {
          padding-bottom: 0;
        }
      }

      .compact-color-field {
        width: 60px;

        .mat-mdc-form-field-wrapper {
          padding-bottom: 0;
        }
      }
    }

    .compact-departments-row {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .compact-dept-select {
        min-width: 300px;

        .mat-mdc-form-field-wrapper {
          padding-bottom: 0;
        }
      }

      .compact-selected-chips {
        flex: 1;

        mat-chip-listbox {
          mat-chip-option {
            margin: 2px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
          }
        }
      }
    }
  }
}

// Compact empty state
.compact-empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
  background: #f9f9f9;
  border: 2px dashed #ddd;
  border-radius: 8px;
  margin-top: 20px;

  mat-icon {
    font-size: 36px;
    width: 36px;
    height: 36px;
    margin-bottom: 12px;
    color: #ccc;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

// Compact save section
.compact-save-section {
  display: flex;
  justify-content: center;
  padding: 24px 0;
  border-top: 1px solid #e0e0e0;
  margin-top: 24px;

  .compact-save-button {
    background: #4caf50;
    color: white;
    min-width: 180px;
    height: 44px;
    font-weight: 600;

    &:hover:not([disabled]) {
      background: #45a049;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    &[disabled] {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .button-spinner {
      margin-right: 8px;
    }
  }
}
